@extends('app')

@section('content')
    @include('includes.header')
    <section class="cover">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="spead">
                        <h1>{{ $course->title }}</h1>
                    </div>
                    <div class="scroll-down">
                        <div class="rounded-text rotating">
                            <svg viewBox="0 0 200 200">
                                <path id="textPath" d="M 85,0 A 85,85 0 0 1 -85,0 A 85,85 0 0 1 85,0" transform="translate(100,100)" fill="none" stroke-width="0"></path>
                                <g font-size="13.1px">
                                    <text text-anchor="start">
                                        <textPath class="coloring" xlink:href="#textPath" startOffset="0%">SCROLL DOWN - COURSE DETAILS - SCROLL DOWN - COURSE DETAILS - SCROLL DOWN - COURSE DETAILS</textPath>
                                    </text>
                                </g>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bg-img" data-img="{{Storage::url($course->cover)}}"></div>
    </section>

    <section class="courses-full">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="courses-full__infos2 mb-5">
                        <div class="spead">
                            <ul>
                                <li><a href="#"><svg width="800px" height="800px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M18.4384 20C19.3561 20 20.1493 19.3726 20.2725 18.4632C20.3895 17.5988 20.5 16.4098 20.5 15C20.5 12 20.6683 10.1684 17.5 7C16.0386 5.53865 14.4064 4.41899 13.3024 3.74088C12.4978 3.24665 11.5021 3.24665 10.6975 3.74088C9.5935 4.41899 7.96131 5.53865 6.49996 7C3.33157 10.1684 3.49997 12 3.49997 15C3.49997 16.4098 3.61039 17.5988 3.72745 18.4631C3.85061 19.3726 4.64378 20 5.56152 20H18.4384Z" stroke="#000000" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg> @lang("common.home")</a></li>
                                <li><a href="{{route('course.index')}}">@lang("common.courses")</a></li>
                                <li class="active">{{$course->title}}</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-12 mt-2">
                    <div class="section-title">
                        <h1>@lang("common.choose_course")</h1>
                        <ul>
                            <li class="sprev2"><a href="#"><i class="icofont-rounded-left"></i></a></li>
                            <li class="snext2"><a href="#"><i class="icofont-rounded-right"></i></a></li>
                        </ul>
                    </div>
                    <div class="choose">
                        <div class="choose-carousel swiper-container" id="choose">
                            <div class="swiper-wrapper">
                                @foreach($upcomingLocations as $location)
                                    <div class="swiper-slide">
                                        <div class="choose-item" data-toggle="modal" data-target="#register">
                                            <div class="country">
                                                <img src="{{Storage::url($location->image)}}" alt="" />
                                                <h2>{{$location->name}}</h2>

                                            </div>
                                            <div class="date">
                                                <span>
                                                    <svg width="800px" height="800px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M20 10V7C20 5.89543 19.1046 5 18 5H6C4.89543 5 4 5.89543 4 7V10M20 10V19C20 20.1046 19.1046 21 18 21H6C4.89543 21 4 20.1046 4 19V10M20 10H4M8 3V7M16 3V7" stroke="#000000" stroke-width="2" stroke-linecap="round"/>
                                                        <rect x="6" y="12" width="3" height="3" rx="0.5" fill="#000000"/>
                                                        <rect x="10.5" y="12" width="3" height="3" rx="0.5" fill="#000000"/>
                                                        <rect x="15" y="12" width="3" height="3" rx="0.5" fill="#000000"/>
                                                    </svg>
                                                    {{$location->pivot->upComingFormat}}
                                                </span>
                                                <p>
                                                    <strong>
                                                        <svg fill="#000000" width="800px" height="800px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M21,12.692,11.308,3H3v8.308L12.692,21ZM9.923,9.923a1.958,1.958,0,1,1,0-2.769A1.957,1.957,0,0,1,9.923,9.923Z"/>
                                                        </svg>
                                                        @if($location->pivot->location_id == 1)
                                                            £{{ number_format($location->pivot->price * 1.2, 2) }}
                                                        @else
                                                            £{{$location->pivot->price}}
                                                        @endif
                                                    </strong>

                                                    <a href="{{ route('cart.store', $location->pivot) }}" class="cart-store">
                                                        @lang("common.register")
                                                    </a>

                                                    @if($location->pivot->location_id == 1)
                                                            <div style="margin-top:5px; font-size:12px; color:#000000;font-weight:normal;       ">
                                                            Includes VAT (20%)
                                                        </div>
                                                    @endif
                                                </p>

                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-9">

                    <div class="courses-full__tabs">
                        <ul class="nav nav-tabs" id="myTab" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" id="home-tab" data-toggle="tab" href="#home" role="tab" aria-controls="home" aria-selected="true">
                                    <svg width="800px" height="800px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                        <title>@lang("common.course_overview")</title>
                                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                            <rect x="0" y="0" width="24" height="24"></rect>
                                            <path d="M19,10.5714286 L19,18 C19,19.1045695 18.1045695,20 17,20 L6,20 C4.8954305,20 4,19.1045695 4,18 L4,7 C4,5.8954305 4.8954305,5 6,5 L13.4285714,5 L13.4285714,5" id="shape-1" stroke="#030819" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="0,0"></path>
                                            <path d="M18,7 C18.5522847,7 19,6.55228475 19,6 C19,5.44771525 18.5522847,5 18,5 C17.4477153,5 17,5.44771525 17,6 C17,6.55228475 17.4477153,7 18,7 Z" id="shape-2" stroke="#030819" stroke-width="2" stroke-linecap="round" stroke-dasharray="0,0"></path>
                                            <path d="M8,15 L11,11 L13.000781,13 L16,9" id="shape-3" stroke="#030819" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="0,0"></path>
                                        </g>
                                    </svg> @lang("common.course_overview")
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="profile-tab" data-toggle="tab" href="#profile" role="tab" aria-controls="profile" aria-selected="false">
                                    <svg fill="#000000" width="800px" height="800px" viewBox="0 0 1920 1920" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M1750.176 0v1468.235h-225.882v338.824h169.412V1920H451.353c-82.447 0-161.506-36.141-215.718-99.388-42.917-50.824-66.635-116.33-66.635-182.965V282.353C169 126.494 295.494 0 451.353 0h1298.823Zm-338.823 1468.235H463.776c-89.223 0-166.023 60.989-179.576 140.047-1.13 9.036-2.259 18.07-2.259 25.977v3.388c0 40.659 13.553 79.059 40.659 109.553 31.624 38.4 79.059 59.859 128.753 59.859h960v-112.941H408.435v-112.942h1002.918v-112.94Zm-56.47-564.706h-790.59v112.942h790.588V903.529Zm56.47-564.705h-903.53v451.764h903.53V338.824ZM620.765 677.647h677.647V451.765H620.765v225.882Z" fill-rule="evenodd"/>
                                    </svg> @lang("common.course_outline")
                                </a>
                            </li>

                            @if($course->info)
                            <li class="nav-item">
                                <a class="nav-link" id="info-tab" data-toggle="tab" href="#info" role="tab" aria-controls="profile" aria-selected="false">
                                    <svg fill="#000000" width="800px" height="800px" viewBox="0 0 1920 1920" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M1750.176 0v1468.235h-225.882v338.824h169.412V1920H451.353c-82.447 0-161.506-36.141-215.718-99.388-42.917-50.824-66.635-116.33-66.635-182.965V282.353C169 126.494 295.494 0 451.353 0h1298.823Zm-338.823 1468.235H463.776c-89.223 0-166.023 60.989-179.576 140.047-1.13 9.036-2.259 18.07-2.259 25.977v3.388c0 40.659 13.553 79.059 40.659 109.553 31.624 38.4 79.059 59.859 128.753 59.859h960v-112.941H408.435v-112.942h1002.918v-112.94Zm-56.47-564.706h-790.59v112.942h790.588V903.529Zm56.47-564.705h-903.53v451.764h903.53V338.824ZM620.765 677.647h677.647V451.765H620.765v225.882Z" fill-rule="evenodd"/>
                                    </svg> @lang("common.course_info")
                                </a>
                            </li>
                           @endif

                        </ul>
                        <div class="tab-content" id="myTabContent">
                            <div class="tab-pane fade show active" id="home" role="tabpanel" aria-labelledby="home-tab">
                                <div class="content-in">
                                    {!! $course->overview !!}
                                </div>
                                <div class="btns-in">
                                    <a href="#" data-toggle="modal" data-target="#form"><svg width="800px" height="800px" viewBox="0 0 192 192" xmlns="http://www.w3.org/2000/svg" style="enable-background:new 0 0 192 192" xml:space="preserve"><path d="M104.1 65.7v88.5c6.1-6.3 18.3-17.2 37-23.4 11.5-3.8 21.6-4.6 28.9-4.5V37.8c-8 .5-18.7 2.1-30.7 6.5-16.5 6.2-28.2 15.1-35.2 21.4zm-16.2 0v88.5c-6.1-6.3-18.3-17.2-37-23.4-11.5-3.8-21.6-4.6-28.9-4.5V37.8c8 .5 18.7 2.1 30.7 6.5 16.5 6.2 28.2 15.1 35.2 21.4z" style="fill:none;stroke:#000000;stroke-width:12;stroke-linejoin:round;stroke-miterlimit:10"/></svg> @lang("common.quick_enq")</a>
                                    @if($course->pdf)
                                    <a href="{{Storage::url($course->pdf)}}" target="_blank" rel="noopener noreferrer" class="course-outline-download">
                                        <svg width="800px" height="800px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M10 1C9.73478 1 9.48043 1.10536 9.29289 1.29289L3.29289 7.29289C3.10536 7.48043 3 7.73478 3 8V20C3 21.6569 4.34315 23 6 23H7C7.55228 23 8 22.5523 8 22C8 21.4477 7.55228 21 7 21H6C5.44772 21 5 20.5523 5 20V9H10C10.5523 9 11 8.55228 11 8V3H18C18.5523 3 19 3.44772 19 4V11C19 11.5523 19.4477 12 20 12C20.5523 12 21 11.5523 21 11V4C21 2.34315 19.6569 1 18 1H10ZM9 7H6.41421L9 4.41421V7ZM10.3078 23.5628C10.4657 23.7575 10.6952 23.9172 10.9846 23.9762C11.2556 24.0316 11.4923 23.981 11.6563 23.9212C11.9581 23.8111 12.1956 23.6035 12.3505 23.4506C12.5941 23.2105 12.8491 22.8848 13.1029 22.5169C14.2122 22.1342 15.7711 21.782 17.287 21.5602C18.1297 21.4368 18.9165 21.3603 19.5789 21.3343C19.8413 21.6432 20.08 21.9094 20.2788 22.1105C20.4032 22.2363 20.5415 22.3671 20.6768 22.4671C20.7378 22.5122 20.8519 22.592 20.999 22.6493C21.0755 22.6791 21.5781 22.871 22.0424 22.4969C22.3156 22.2768 22.5685 22.0304 22.7444 21.7525C22.9212 21.4733 23.0879 21.0471 22.9491 20.5625C22.8131 20.0881 22.4588 19.8221 22.198 19.6848C21.9319 19.5448 21.6329 19.4668 21.3586 19.4187C21.11 19.3751 20.8288 19.3478 20.5233 19.3344C19.9042 18.5615 19.1805 17.6002 18.493 16.6198C17.89 15.76 17.3278 14.904 16.891 14.1587C16.9359 13.9664 16.9734 13.7816 17.0025 13.606C17.0523 13.3052 17.0824 13.004 17.0758 12.7211C17.0695 12.4497 17.0284 12.1229 16.88 11.8177C16.7154 11.4795 16.416 11.1716 15.9682 11.051C15.5664 10.9428 15.1833 11.0239 14.8894 11.1326C14.4359 11.3004 14.1873 11.6726 14.1014 12.0361C14.0288 12.3437 14.0681 12.6407 14.1136 12.8529C14.2076 13.2915 14.4269 13.7956 14.6795 14.2893C14.702 14.3332 14.7251 14.3777 14.7487 14.4225C14.5103 15.2072 14.1578 16.1328 13.7392 17.0899C13.1256 18.4929 12.4055 19.8836 11.7853 20.878C11.3619 21.0554 10.9712 21.2584 10.6746 21.4916C10.4726 21.6505 10.2019 21.909 10.0724 22.2868C9.9132 22.7514 10.0261 23.2154 10.3078 23.5628ZM11.8757 23.0947C11.8755 23.0946 11.8775 23.0923 11.8824 23.0877C11.8783 23.0924 11.8759 23.0947 11.8757 23.0947ZM16.9974 19.5812C16.1835 19.7003 15.3445 19.8566 14.5498 20.0392C14.9041 19.3523 15.2529 18.6201 15.5716 17.8914C15.7526 17.4775 15.9269 17.0581 16.0885 16.6431C16.336 17.0175 16.5942 17.3956 16.8555 17.7681C17.2581 18.3421 17.6734 18.911 18.0759 19.4437C17.7186 19.4822 17.3567 19.5287 16.9974 19.5812ZM16.0609 12.3842C16.0608 12.3829 16.0607 12.3823 16.0606 12.3823C16.0606 12.3822 16.0607 12.3838 16.061 12.3872C16.061 12.386 16.0609 12.385 16.0609 12.3842Z" fill="#000000"/>
                                        </svg>
                                        @lang("common.download_course_outline")
                                    </a>
                                    @endif
                                    <a href="#" class="scroll-up"><svg width="800px" height="800px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M15 11C15 12.6569 13.6569 14 12 14C10.3431 14 9 12.6569 9 11M4 7H20M4 7V13C4 19.3668 5.12797 20.5 12 20.5C18.872 20.5 20 19.3668 20 13V7M4 7L5.44721 4.10557C5.786 3.428 6.47852 3 7.23607 3H16.7639C17.5215 3 18.214 3.428 18.5528 4.10557L20 7" stroke="#000000" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                        </svg>
                                        @lang("common.register")
                                    </a>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="profile" role="tabpanel" aria-labelledby="profile-tab">
                                <div class="content-in">
                                    {!! $course->outline !!}
                                </div>
                                <div class="btns-in">
                                    <a href="#" data-toggle="modal" data-target="#form"><svg width="800px" height="800px" viewBox="0 0 192 192" xmlns="http://www.w3.org/2000/svg" style="enable-background:new 0 0 192 192" xml:space="preserve"><path d="M104.1 65.7v88.5c6.1-6.3 18.3-17.2 37-23.4 11.5-3.8 21.6-4.6 28.9-4.5V37.8c-8 .5-18.7 2.1-30.7 6.5-16.5 6.2-28.2 15.1-35.2 21.4zm-16.2 0v88.5c-6.1-6.3-18.3-17.2-37-23.4-11.5-3.8-21.6-4.6-28.9-4.5V37.8c8 .5 18.7 2.1 30.7 6.5 16.5 6.2 28.2 15.1 35.2 21.4z" style="fill:none;stroke:#000000;stroke-width:12;stroke-linejoin:round;stroke-miterlimit:10"/></svg> @lang("common.quick_enq")</a>
                                    @if($course->pdf)
                                        <a href="{{Storage::url($course->pdf)}}" target="_blank" class="course-outline-download">
                                            <svg width="800px" height="800px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" clip-rule="evenodd" d="M10 1C9.73478 1 9.48043 1.10536 9.29289 1.29289L3.29289 7.29289C3.10536 7.48043 3 7.73478 3 8V20C3 21.6569 4.34315 23 6 23H7C7.55228 23 8 22.5523 8 22C8 21.4477 7.55228 21 7 21H6C5.44772 21 5 20.5523 5 20V9H10C10.5523 9 11 8.55228 11 8V3H18C18.5523 3 19 3.44772 19 4V11C19 11.5523 19.4477 12 20 12C20.5523 12 21 11.5523 21 11V4C21 2.34315 19.6569 1 18 1H10ZM9 7H6.41421L9 4.41421V7ZM10.3078 23.5628C10.4657 23.7575 10.6952 23.9172 10.9846 23.9762C11.2556 24.0316 11.4923 23.981 11.6563 23.9212C11.9581 23.8111 12.1956 23.6035 12.3505 23.4506C12.5941 23.2105 12.8491 22.8848 13.1029 22.5169C14.2122 22.1342 15.7711 21.782 17.287 21.5602C18.1297 21.4368 18.9165 21.3603 19.5789 21.3343C19.8413 21.6432 20.08 21.9094 20.2788 22.1105C20.4032 22.2363 20.5415 22.3671 20.6768 22.4671C20.7378 22.5122 20.8519 22.592 20.999 22.6493C21.0755 22.6791 21.5781 22.871 22.0424 22.4969C22.3156 22.2768 22.5685 22.0304 22.7444 21.7525C22.9212 21.4733 23.0879 21.0471 22.9491 20.5625C22.8131 20.0881 22.4588 19.8221 22.198 19.6848C21.9319 19.5448 21.6329 19.4668 21.3586 19.4187C21.11 19.3751 20.8288 19.3478 20.5233 19.3344C19.9042 18.5615 19.1805 17.6002 18.493 16.6198C17.89 15.76 17.3278 14.904 16.891 14.1587C16.9359 13.9664 16.9734 13.7816 17.0025 13.606C17.0523 13.3052 17.0824 13.004 17.0758 12.7211C17.0695 12.4497 17.0284 12.1229 16.88 11.8177C16.7154 11.4795 16.416 11.1716 15.9682 11.051C15.5664 10.9428 15.1833 11.0239 14.8894 11.1326C14.4359 11.3004 14.1873 11.6726 14.1014 12.0361C14.0288 12.3437 14.0681 12.6407 14.1136 12.8529C14.2076 13.2915 14.4269 13.7956 14.6795 14.2893C14.702 14.3332 14.7251 14.3777 14.7487 14.4225C14.5103 15.2072 14.1578 16.1328 13.7392 17.0899C13.1256 18.4929 12.4055 19.8836 11.7853 20.878C11.3619 21.0554 10.9712 21.2584 10.6746 21.4916C10.4726 21.6505 10.2019 21.909 10.0724 22.2868C9.9132 22.7514 10.0261 23.2154 10.3078 23.5628ZM11.8757 23.0947C11.8755 23.0946 11.8775 23.0923 11.8824 23.0877C11.8783 23.0924 11.8759 23.0947 11.8757 23.0947ZM16.9974 19.5812C16.1835 19.7003 15.3445 19.8566 14.5498 20.0392C14.9041 19.3523 15.2529 18.6201 15.5716 17.8914C15.7526 17.4775 15.9269 17.0581 16.0885 16.6431C16.336 17.0175 16.5942 17.3956 16.8555 17.7681C17.2581 18.3421 17.6734 18.911 18.0759 19.4437C17.7186 19.4822 17.3567 19.5287 16.9974 19.5812ZM16.0609 12.3842C16.0608 12.3829 16.0607 12.3823 16.0606 12.3823C16.0606 12.3822 16.0607 12.3838 16.061 12.3872C16.061 12.386 16.0609 12.385 16.0609 12.3842Z" fill="#000000"/>
                                            </svg>
                                            @lang("common.download_course_outline")
                                        </a>
                                    @endif
                                    <a href="#" class="scroll-up"><svg width="800px" height="800px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M15 11C15 12.6569 13.6569 14 12 14C10.3431 14 9 12.6569 9 11M4 7H20M4 7V13C4 19.3668 5.12797 20.5 12 20.5C18.872 20.5 20 19.3668 20 13V7M4 7L5.44721 4.10557C5.786 3.428 6.47852 3 7.23607 3H16.7639C17.5215 3 18.214 3.428 18.5528 4.10557L20 7" stroke="#000000" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                        </svg>
                                        @lang("common.register")
                                    </a>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="info" role="tabpanel" aria-labelledby="info-tab">
                                <div class="content-in">
                                    {!! $course->info !!}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="courses-full__admin">
                        <div class="us">
                            <div class="us-item">
                                <div class="us-item__title">
                                    <h2>@lang("common.support_title")</h2>
                                </div>
                                <!-- /.us-item__title -->
                                <div class="us-item__img">
                                    <figure><img src="{{Storage::url($course->lecturer?->image)}}" alt="{{$course->lecturer?->name}}" /></figure>
                                </div>
                                <div class="us-item__title">
                                    <h3>{{$course->lecturer?->name}}</h3>
                                    <h4><a href="mailto:<EMAIL>">{{$course->lecturer?->email}}</a></h4>
                                    @if($c = data_get($course->lecturer?->contacts, 'mobile'))
                                        <h4><a href="tel:{{$c}}">{{$c}}</a></h4>
                                    @endif
                                    @if($c = data_get($course->lecturer?->contacts, 'whatsapp'))
                                    <h5 class="whatsapp">
                                        <a href="https://wa.me/{{ preg_replace('/[^0-9]/', '', $c) }}" target="_blank">
                                            <svg width="800px" height="800px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M17.6 6.31999C16.8669 5.58141 15.9943 4.99596 15.033 4.59767C14.0716 4.19938 13.0406 3.99622 12 3.99999C10.6089 4.00135 9.24248 4.36819 8.03771 5.06377C6.83294 5.75935 5.83208 6.75926 5.13534 7.96335C4.4386 9.16745 4.07046 10.5335 4.06776 11.9246C4.06507 13.3158 4.42793 14.6832 5.12 15.89L4 20L8.2 18.9C9.35975 19.5452 10.6629 19.8891 11.99 19.9C14.0997 19.9001 16.124 19.0668 17.6222 17.5816C19.1205 16.0965 19.9715 14.0796 19.99 11.97C19.983 10.9173 19.7682 9.87634 19.3581 8.9068C18.948 7.93725 18.3505 7.05819 17.6 6.31999ZM12 18.53C10.8177 18.5308 9.65701 18.213 8.64 17.61L8.4 17.46L5.91 18.12L6.57 15.69L6.41 15.44C5.55925 14.0667 5.24174 12.429 5.51762 10.8372C5.7935 9.24545 6.64361 7.81015 7.9069 6.80322C9.1702 5.79628 10.7589 5.28765 12.3721 5.37368C13.9853 5.4597 15.511 6.13441 16.66 7.26999C17.916 8.49818 18.635 10.1735 18.66 11.93C18.6442 13.6859 17.9355 15.3645 16.6882 16.6006C15.441 17.8366 13.756 18.5301 12 18.53ZM15.61 13.59C15.41 13.49 14.44 13.01 14.26 12.95C14.08 12.89 13.94 12.85 13.81 13.05C13.6144 13.3181 13.404 13.5751 13.18 13.82C13.07 13.96 12.95 13.97 12.75 13.82C11.6097 13.3694 10.6597 12.5394 10.06 11.47C9.85 11.12 10.26 11.14 10.64 10.39C10.6681 10.3359 10.6827 10.2759 10.6827 10.215C10.6827 10.1541 10.6681 10.0941 10.64 10.04C10.64 9.93999 10.19 8.95999 10.03 8.56999C9.87 8.17999 9.71 8.23999 9.58 8.22999H9.19C9.08895 8.23154 8.9894 8.25465 8.898 8.29776C8.8066 8.34087 8.72546 8.403 8.66 8.47999C8.43562 8.69817 8.26061 8.96191 8.14676 9.25343C8.03291 9.54495 7.98287 9.85749 8 10.17C8.0627 10.9181 8.34443 11.6311 8.81 12.22C9.6622 13.4958 10.8301 14.5293 12.2 15.22C12.9185 15.6394 13.7535 15.8148 14.58 15.72C14.8552 15.6654 15.1159 15.5535 15.345 15.3915C15.5742 15.2296 15.7667 15.0212 15.91 14.78C16.0428 14.4856 16.0846 14.1583 16.03 13.84C15.94 13.74 15.81 13.69 15.61 13.59Z" fill="#000000"/>
                                            </svg>
                                            @lang('common.whatsapp')
                                        </a>
                                    </h5>
                                @endif
                                    <ul>
                                        @if($c = data_get($course->lecturer?->contacts, 'fb'))
                                            <li><a href="{{$c}}"><i class="icofont-facebook"></i></a></li>
                                        @endif
                                        @if($c = data_get($course->lecturer?->contacts, 'twitter'))
                                            <li><a href="{{$c}}"><i class="icofont-twitter"></i></a></li>
                                        @endif
                                        @if($c = data_get($course->lecturer?->contacts, 'linkedin'))
                                            <li><a href="{{$c}}"><i class="icofont-linkedin"></i></a></li>
                                        @endif
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="tx">
                            <p>{!! $course->lecturer?->bio !!}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="section why_us">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="section-title">
                        <span>@lang("common.why")</span>
                        <h1>@lang("common.choose_us")</h1>
                    </div>
                </div>
                @foreach(\App\Models\WhyChooseU::all() as $w)
                <div class="col-md-3">
                    <div class="why_us-item">
                        <figure>
                            <img src="{{Storage::url($w->img)}}" alt="">
                        </figure>
                        <div class="title-in">
                            <h3>{{$w->title}}</h3>
                            <p>{{$w->desc}}</p>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </section>

    <section class="section faq">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="section-title">
                        <span>@lang("common.faq_title_1")</span>
                        <h1>@lang("common.faq_title_2")</h1>
                    </div>
                    <!-- /.section-title -->
                </div>
                <!-- /.col-md-12 -->
                <div class="col-md-5">
                    <figure>

                        <svg xmlns="http://www.w3.org/2000/svg" data-name="Layer 1" width="844.67538" height="595.26155" viewBox="0 0 844.67538 595.26155" xmlns:xlink="http://www.w3.org/1999/xlink"><circle cx="431.39281" cy="548" r="46" fill="#d02333"/><path d="M883.86176,743.78487c25.7345-7.72868,53.09381-15.78786,73.50313-34.161,18.23763-16.41813,30.55024-41.48912,22.99475-66.1115-7.54-24.57187-30.12421-40.95629-53.44165-49.10532-13.225-4.62188-27.06087-7.18608-40.89147-9.20037-15.03485-2.18967-30.13615-3.98373-45.23026-5.71012q-91.67724-10.48563-184.04386-12.811c-30.38456-.76525-60.76358-.74682-91.15243-.3057-27.13937.394-55.72215.38417-80.899-11.15051-19.57846-8.96979-37.348-25.28881-42.8033-46.73352-6.29728-24.75467,5.318-49.96382,21.97993-67.892,8.78265-9.45011,19.04731-17.40385,29.63621-24.71743,11.4874-7.93416,23.37539-15.30643,35.52084-22.18813a494.63414,494.63414,0,0,1,74.7667-34.4685c12.74555-4.63445,25.68047-8.63281,38.72759-12.32143,11.017-3.11469,22.06832-6.23382,32.71588-10.47748,20.58349-8.20371,40.161-22.09985,45.39464-44.88142,4.96024-21.59145-3.40305-45.03067-18.065-61.07057-16.96282-18.557-42.53949-26.69181-67.06007-28.008-27.52842-1.47765-54.42246,5.412-80.29678,14.15585-27.59673,9.326-54.59854,20.04924-82.77827,27.60322a556.95783,556.95783,0,0,1-85.19574,15.83655c-14.08227,1.49951-28.59019,3.19273-42.75626,2.04475-11.87246-.96211-23.68426-4.45375-32.43408-12.87964-7.50252-7.22477-11.97184-17.154-10.4353-27.63238.27909-1.90318,3.17022-1.09407,2.89284.79752-1.8704,12.75513,6.79991,24.50863,17.48415,30.5293,12.34817,6.95832,27.37408,6.9678,41.1218,6.172a537.82528,537.82528,0,0,0,88.51452-12.79561c28.59252-6.53059,56.16382-15.86633,83.70391-25.83908,26.15594-9.47153,52.89665-18.71579,80.84009-20.76729,24.24611-1.78007,49.75165,1.75222,70.87426,14.42313,18.56387,11.136,32.21482,29.70722,36.56451,51.01813,4.25044,20.82462-1.63632,41.785-17.4,56.31714-16.32147,15.04633-38.7007,21.47909-59.55655,27.40368-26.45223,7.51437-52.33726,16.29809-77.39248,27.7031a485.82354,485.82354,0,0,0-72.8001,40.92805c-22.24625,15.20228-44.2007,34.33058-51.23658,61.45126-3.27739,12.63313-2.67227,26.03212,2.8116,37.96461,4.87605,10.60993,12.90656,19.53469,22.26169,26.41853,22.32074,16.42443,50.45266,19.79665,77.41421,20.13212,30.28143.37678,60.56382-.64518,90.85508-.148q92.5988,1.51977,184.81863,11.27265,23.108,2.44594,46.15759,5.40711c13.82158,1.776,27.68967,3.54058,41.27849,6.69464,24.16222,5.60822,47.67389,16.39167,62.69178,36.878a61.31938,61.31938,0,0,1,11.94709,30.44593c1.05134,11.52384-1.76985,23.0693-6.98016,33.32083-11.53233,22.69042-33.13363,37.12286-56.07337,46.60471-12.28683,5.0786-25.03167,8.926-37.7516,12.74609-1.853.55651-2.64487-2.338-.79752-2.89283Z" transform="translate(-177.66231 -152.36922)" fill="#f2f2f2"/><circle cx="125.89281" cy="69.5" r="24" fill="#f2f2f2"/><circle cx="292.39281" cy="115" r="24" fill="#f2f2f2"/><circle cx="433.39281" cy="24" r="24" fill="#f2f2f2"/><circle cx="521.39281" cy="126" r="24" fill="#f2f2f2"/><circle cx="402.39281" cy="244" r="24" fill="#f2f2f2"/><circle cx="251.39281" cy="301" r="24" fill="#f2f2f2"/><circle cx="411.39281" cy="390" r="24" fill="#f2f2f2"/><circle cx="583.39281" cy="440" r="24" fill="#f2f2f2"/><circle cx="784.39281" cy="429" r="24" fill="#f2f2f2"/><path d="M604.12763,220.37264c-71.89185.50782-130.75611,58.92987-131.77735,130.81635-.00946.66369-.01381,5.33048-.01324,11.43422a33.74778,33.74778,0,0,0,33.74387,33.746h.00007a33.76855,33.76855,0,0,0,33.76114-33.79775c-.00343-4.15211-.00551-7.02584-.00551-7.20227a64.00037,64.00037,0,1,1,98.52027,53.8794l.01171.01422s-48.02832,30.91956-62.67089,73.33545l.01245.003a94.00389,94.00389,0,0,0-3.87354,26.76794c0,3.72538.21916,36.32138.64261,62.77767a34.78649,34.78649,0,0,0,34.79009,34.22233h.00007a34.79588,34.79588,0,0,0,34.79384-35.01061c-.14706-24.22912-.22661-52.44168-.22661-54.48939,0-26.04473,25.12525-51.99475,45.76367-68.91741,23.76587-19.48694,40.86792-46.04291,47.73706-75.99909a86.7618,86.7618,0,0,0,2.49927-18.8335A132.75,132.75,0,0,0,604.12763,220.37264Z" transform="translate(-177.66231 -152.36922)" fill="#d02333"/><path d="M1021.147,747.63078H178.853a1.19069,1.19069,0,0,1,0-2.38137h842.294a1.19068,1.19068,0,0,1,0,2.38137Z" transform="translate(-177.66231 -152.36922)" fill="#3f3d56"/><circle cx="628.44885" cy="242.9959" r="30" fill="#2f2e41"/><polygon points="573.012 582.129 560.753 582.129 554.92 534.841 573.015 534.841 573.012 582.129" fill="#a0616a"/><path d="M551.99554,578.62562h23.64387a0,0,0,0,1,0,0v14.88687a0,0,0,0,1,0,0H537.10868a0,0,0,0,1,0,0v0A14.88686,14.88686,0,0,1,551.99554,578.62562Z" fill="#2f2e41"/><polygon points="668.012 582.129 655.753 582.129 649.92 534.841 668.015 534.841 668.012 582.129" fill="#a0616a"/><path d="M646.99554,578.62562h23.64387a0,0,0,0,1,0,0v14.88687a0,0,0,0,1,0,0H632.10868a0,0,0,0,1,0,0v0A14.88686,14.88686,0,0,1,646.99554,578.62562Z" fill="#2f2e41"/><circle cx="623.88979" cy="248.61007" r="24.56103" fill="#a0616a"/><path d="M816.19123,504.7751l10.98975-25.25a31.38253,31.38253,0,0,0-6.94971-35.6,31.87322,31.87322,0,0,0-3.07031-2.67,30.93522,30.93522,0,0,0-18.98975-6.57,32.179,32.179,0,0,0-13.3999,2.98c-.36035.16-.71.33-1.07031.5-.68994.33-1.36963.69-2.02979,1.06a31.67823,31.67823,0,0,0-15.70019,23.88l-4.8501,40.64c-1.21973,3.19-44.73975,118.39-29.51953,206.34a4.46692,4.46692,0,0,0,3.81982,3.67l15.43018,2.1a4.49661,4.49661,0,0,0,5.00976-3.53l25.89014-123.41a3.50323,3.50323,0,0,1,6.79981-.23l36.58007,129.78a4.47129,4.47129,0,0,0,4.31006,3.28,5.12184,5.12184,0,0,0,.87012-.08l18.84961-3.63a4.471,4.471,0,0,0,3.63037-4.81C850.02131,682.3351,835.3011,527.60512,816.19123,504.7751Z" transform="translate(-177.66231 -152.36922)" fill="#2f2e41"/><path d="M706.10166,421.41909A10.05576,10.05576,0,0,0,716.696,432.6225l13.72894,32.99236,10.385-15.3943-14.62937-28.97a10.11027,10.11027,0,0,0-20.07892.16852Z" transform="translate(-177.66231 -152.36922)" fill="#a0616a"/><path d="M800.19025,537.99553a10.05577,10.05577,0,0,0,8.42651-12.91316l28.88533-21.03846-17.39036-6.51224-24.76387,20.97687a10.11028,10.11028,0,0,0,4.84239,19.487Z" transform="translate(-177.66231 -152.36922)" fill="#a0616a"/><path d="M753.10188,487.61024a17.05692,17.05692,0,0,1-3.29834-.32519,16.30539,16.30539,0,0,1-11.94751-9.61621l-19.23438-23.45313a4.50075,4.50075,0,0,1,1.11109-6.68066l13.68432-8.4707a4.50007,4.50007,0,0,1,6.21533,1.49023l13.5564,22.334L779.15022,443.702A9.72146,9.72146,0,0,1,790.46,459.26356l-25.91186,23.63672A16.25271,16.25271,0,0,1,753.10188,487.61024Z" transform="translate(-177.66231 -152.36922)" fill="#2f2e41"/><path d="M823.252,522.8827c-.03515,0-.07055,0-.10571-.001a4.50783,4.50783,0,0,1-3.31079-1.57031l-12.16626-14.19336a4.49979,4.49979,0,0,1,.92041-6.67286l22.78149-15.1875-20.63842-24.8125a9.721,9.721,0,0,1,14.8872-12.18261l25.0835,24.51269a16.52481,16.52481,0,0,1-3.67529,26.94043l-20.50122,21.75391A4.50742,4.50742,0,0,1,823.252,522.8827Z" transform="translate(-177.66231 -152.36922)" fill="#2f2e41"/><path d="M795.30707,470.58358a4.63212,4.63212,0,0,1-.584-.03711,4.46111,4.46111,0,0,1-3.71045-3.06885l-9.14234-28.02929a3.08255,3.08255,0,0,1,1.594-3.72461l.29663-.14014c.269-.12793.5354-.25439.80737-.37549a32.57412,32.57412,0,0,1,13.603-3.023,31.327,31.327,0,0,1,17.16138,5.15674,3.13007,3.13007,0,0,1,.90136,4.29443L799.08393,468.504A4.45513,4.45513,0,0,1,795.30707,470.58358Z" transform="translate(-177.66231 -152.36922)" fill="#e6e6e6"/><circle cx="652.1011" cy="219.78616" r="9.81668" fill="#2f2e41"/><path d="M796.11115,361.36513h0a26,26,0,0,0-26,25.99994v11.00006h13.5293l6.4707-11,1.94141,11h41.05859l-11-11.00006A26,26,0,0,0,796.11115,361.36513Z" transform="translate(-177.66231 -152.36922)" fill="#2f2e41"/><path d="M834.80883,365.43121a15.15,15.15,0,0,1,16.48081-10.39558c6.256,1.04586,11.20228,6.07455,14.14944,11.69107s4.30806,11.90252,6.28935,17.92793,4.79124,12.08362,9.79306,15.984,12.67721,4.9584,17.58966.94607a20.11809,20.11809,0,0,1-37.47706,7.18124c-2.59206-4.61172-3.26121-10.01684-4.02988-15.251s-1.7674-10.6498-4.86211-14.94043-8.88772-7.09293-13.80374-5.13859Z" transform="translate(-177.66231 -152.36922)" fill="#2f2e41"/><path d="M515.60883,380.40755h0a33.748,33.748,0,0,1-33.74414-33.746c-.00049-6.10376.0039-10.77051.01318-11.4342a131.50724,131.50724,0,0,1,15.35889-59.90875,131.80321,131.80321,0,0,0-25.35889,75.90875c-.00928.66369-.01367,5.33044-.01318,11.4342a33.748,33.748,0,0,0,33.74414,33.746h0A33.77281,33.77281,0,0,0,538.09662,371.817,33.62247,33.62247,0,0,1,515.60883,380.40755Z" transform="translate(-177.66231 -152.36922)" fill="#3f3d56"/><path d="M606.415,291.47848a64.00385,64.00385,0,0,1,55.65918,89.413,63.9972,63.9972,0,1,0-107.42578-66.98523A63.87073,63.87073,0,0,1,606.415,291.47848Z" transform="translate(-177.66231 -152.36922)" fill="#3f3d56"/><path d="M616.79682,590.40755h0a34.78682,34.78682,0,0,1-34.79-34.22235c-.42334-26.45629-.64258-59.0523-.64258-62.77765a94.00389,94.00389,0,0,1,3.87354-26.76794l-.01221-.003a95.069,95.069,0,0,1,5.49414-12.70087,110.04745,110.04745,0,0,0-15.49414,28.70087l.01221.003a94.00389,94.00389,0,0,0-3.87354,26.76794c0,3.72535.21924,36.32136.64258,62.77765a34.78682,34.78682,0,0,0,34.79,34.22235h0a34.80287,34.80287,0,0,0,33.40185-25.04846A34.66005,34.66005,0,0,1,616.79682,590.40755Z" transform="translate(-177.66231 -152.36922)" fill="#3f3d56"/><polygon points="126.541 582.585 138.8 582.584 144.633 535.296 126.538 535.297 126.541 582.585" fill="#ffb8b8"/><path d="M301.576,731.45065H340.1067a0,0,0,0,1,0,0v14.88687a0,0,0,0,1,0,0H316.46283A14.88686,14.88686,0,0,1,301.576,731.45066v0A0,0,0,0,1,301.576,731.45065Z" transform="translate(464.05409 1325.40429) rotate(179.99738)" fill="#2f2e41"/><polygon points="82.541 582.585 94.8 582.584 100.633 535.296 82.538 535.297 82.541 582.585" fill="#ffb8b8"/><path d="M257.576,731.45065H296.1067a0,0,0,0,1,0,0v14.88687a0,0,0,0,1,0,0H272.46283A14.88686,14.88686,0,0,1,257.576,731.45066v0A0,0,0,0,1,257.576,731.45065Z" transform="translate(376.05409 1325.4063) rotate(179.99738)" fill="#2f2e41"/><path d="M270.91659,720.41068l-11.975-.62988a4.6735,4.6735,0,0,1-4.41851-4.967l14.31268-158.46594,65.911,17.78562,6.35023-1.73241L321.23868,712.68583a4.69622,4.69622,0,0,1-4.35816,3.94458l-12.9089.60147a4.67413,4.67413,0,0,1-4.93149-4.79557l2.339-84.19641a1.55813,1.55813,0,0,0-3.0832-.36007L275.739,716.69228a4.64568,4.64568,0,0,1-4.56913,3.7255C271.086,720.41778,271.00154,720.41575,270.91659,720.41068Z" transform="translate(-177.66231 -152.36922)" fill="#2f2e41"/><circle cx="128.74202" cy="249.75879" r="24.56103" fill="#ffb8b8"/><path d="M265.51193,474.28693l2.70056,58.26748.97625,21.19852a4.64221,4.64221,0,0,0,3.07432,4.17534l63.336,22.94342a4.47742,4.47742,0,0,0,1.59954.28045,4.64358,4.64358,0,0,0,4.66371-4.7881L339.2657,471.5969A36.93044,36.93044,0,0,0,308.522,435.91974c-.61263-.09345-1.23592-.18695-1.8592-.27006a36.24947,36.24947,0,0,0-29.165,9.44122,37.23612,37.23612,0,0,0-11.9859,29.196Z" transform="translate(-177.66231 -152.36922)" fill="#ccc"/><path d="M365.85452,569.24512a10.06355,10.06355,0,0,1-5.36877-15.22659l-21.478-28.56,18.53424-1.14707,17.55439,27.29693a10.111,10.111,0,0,1-9.24184,17.63673Z" transform="translate(-177.66231 -152.36922)" fill="#ffb8b8"/><path d="M350.75332,548.85022a4.64437,4.64437,0,0,1-2.54106-2.51848L315.854,469.2374a12.4634,12.4634,0,1,1,22.98438-9.64693l32.3582,77.09534a4.679,4.679,0,0,1-2.50048,6.11822l-14.36542,6.029a4.64165,4.64165,0,0,1-3.57741.01724Z" transform="translate(-177.66231 -152.36922)" fill="#ccc"/><path d="M298.50776,546.13086,329.587,486.62205a4.87826,4.87826,0,0,1,6.57494-2.06344l45.11152,23.5601a4.87826,4.87826,0,0,1,2.06343,6.57494l-31.07927,59.50881a4.87827,4.87827,0,0,1-6.57494,2.06344L300.5712,552.7058A4.87826,4.87826,0,0,1,298.50776,546.13086Z" transform="translate(-177.66231 -152.36922)" fill="#3f3d56"/><path d="M319.35062,518.94278a10.06358,10.06358,0,0,0-15.517-4.46026l-29.77845-19.75406-.05061,18.56963L302.2904,529.21a10.111,10.111,0,0,0,17.06022-10.26718Z" transform="translate(-177.66231 -152.36922)" fill="#ffb8b8"/><path d="M281.7006,523.11883l-24.33677-19.27776a17.16326,17.16326,0,0,1-7.82343-27.13518l22.09715-28.95951a10.096,10.096,0,0,1,17.1296,10.28435l-17.48384,28.6,25.694,12.18686a4.67363,4.67363,0,0,1,1.94814,6.71958l-10.37175,16.41406a4.682,4.682,0,0,1-3.16671,2.1111c-.02565.00448-.05149.00846-.0773.0123A4.69555,4.69555,0,0,1,281.7006,523.11883Z" transform="translate(-177.66231 -152.36922)" fill="#ccc"/><path d="M287.84537,418.57447a2.13479,2.13479,0,0,1,1.85636-2.81905,4.93046,4.93046,0,0,1,3.4761,1.71495,13.8334,13.8334,0,0,0,3.07115,2.63711c1.18812.59889,2.79953.51354,3.47685-.62824.63605-1.07221.20023-2.508-.18482-3.75347a36.90711,36.90711,0,0,1-1.62991-9.77c-.11092-3.70032.41115-7.562,2.45972-10.44807,2.64387-3.72475,7.37142-5.13883,11.84544-5.0363s8.87547,1.48362,13.30713,2.35665c1.52992.30139,3.32826.4555,4.35153-.73025,1.08805-1.26082.68844-3.3014.22563-5.00376-1.20094-4.41743-2.475-8.98461-5.26525-12.55224a18.89839,18.89839,0,0,0-12.06081-6.79014,28.93848,28.93848,0,0,0-13.46236,1.52838,36.09628,36.09628,0,0,0-17.68285,12.3186,29.23592,29.23592,0,0,0-5.57809,21.60019,26.66712,26.66712,0,0,0,9.88579,16.85462Z" transform="translate(-177.66231 -152.36922)" fill="#2f2e41"/><path d="M598.92043,735.14922a45.99375,45.99375,0,0,1-17.07033-71.4888,45.99715,45.99715,0,1,0,62.56892,66.464A45.96919,45.96919,0,0,1,598.92043,735.14922Z" transform="translate(-177.66231 -152.36922)" fill="#3f3d56"/></svg>
                    </figure>
                </div>
                <div class="col-md-7">
                    <div id="accordionExample" class="accordion shadow" itemscope="" itemtype="https://schema.org/FAQPage">
                        @foreach(\App\Models\Faq::all() as $f)
                        <div class="card" itemscope="" itemprop="mainEntity" itemtype="https://schema.org/Question">
                            <div id="headingOne" class="card-header bg-white shadow-sm border-0" itemprop="name">
                                <h2 class="mb-0">
                                    <button type="button" data-toggle="collapse" data-target="#collapse-{{$loop->index}}" aria-expanded="true"
                                            aria-controls="collapseOne"
                                            class="btn btn-link text-dark font-weight-bold text-uppercase collapsible-link">Q. {{$f->question}}</button>
                                </h2>
                            </div>
                            <div id="collapse-{{$loop->index}}" itemscope="" itemprop="acceptedAnswer" itemtype="https://schema.org/Answer" aria-labelledby="headingOne" data-parent="#accordionExample" class="collapse @if($loop->index === 0) show @endif">
                                <div class="card-body p-5" itemprop="text">
                                    {!! $f->answer !!}
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </section>

    @if($related->count())
    <section class="section related partners">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="section-title">
                        <span>
                            Explore more courses like this
                        </span>
                        <h1>@lang("common.related_course")</h1>
                        <ul>
                            <li class="s-p"><a><span class="icofont-rounded-left"></span></a></li>
                            <li class="s-n"><a><span class="icofont-rounded-right"></span></a></li>
                        </ul>
                    </div>
                </div>
                <div class="swiper-container related-slider">
                    <div class="swiper-wrapper">


                @foreach($related as $r)
               <div class="swiper-slide">
                    <div class="l">
                        <div class="l-item">
                            <div class="l-item__title">
                                <span>{{$r->course->category?->name}}</span>
                                <h2><a href="{{route('course.show', $r->course)}}">{{$r->course->title}}</a></h2>
                                <ul>
                                    <li>
                                        <figure>
                                            <svg width="800px" height="800px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="#292D32" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                                <path d="M7.99998 3H8.99998C7.04998 8.84 7.04998 15.16 8.99998 21H7.99998" stroke="#292D32" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                                <path d="M15 3C16.95 8.84 16.95 15.16 15 21" stroke="#292D32" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                                <path d="M3 16V15C8.84 16.95 15.16 16.95 21 15V16" stroke="#292D32" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                                <path d="M3 9.0001C8.84 7.0501 15.16 7.0501 21 9.0001" stroke="#292D32" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </figure>
                                        <div class="title-init">
                                            <p>Location</p>
                                            <span>{{$r->location->name}}</span>
                                        </div>
                                    </li>
                                    <li>
                                        <figure>
                                            <svg fill="#000000" width="800px" height="800px" viewBox="0 0 24 24" id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg"><path d="M24,12a1,1,0,0,1-2,0A10.011,10.011,0,0,0,12,2a1,1,0,0,1,0-2A12.013,12.013,0,0,1,24,12Zm-8,1a1,1,0,0,0,0-2H13.723A2,2,0,0,0,13,10.277V7a1,1,0,0,0-2,0v3.277A1.994,1.994,0,1,0,13.723,13ZM1.827,6.784a1,1,0,1,0,1,1A1,1,0,0,0,1.827,6.784ZM2,12a1,1,0,1,0-1,1A1,1,0,0,0,2,12ZM12,22a1,1,0,1,0,1,1A1,1,0,0,0,12,22ZM4.221,3.207a1,1,0,1,0,1,1A1,1,0,0,0,4.221,3.207ZM7.779.841a1,1,0,1,0,1,1A1,1,0,0,0,7.779.841ZM1.827,15.216a1,1,0,1,0,1,1A1,1,0,0,0,1.827,15.216Zm2.394,3.577a1,1,0,1,0,1,1A1,1,0,0,0,4.221,18.793Zm3.558,2.366a1,1,0,1,0,1,1A1,1,0,0,0,7.779,21.159Zm14.394-5.943a1,1,0,1,0,1,1A1,1,0,0,0,22.173,15.216Zm-2.394,3.577a1,1,0,1,0,1,1A1,1,0,0,0,19.779,18.793Zm-3.558,2.366a1,1,0,1,0,1,1A1,1,0,0,0,16.221,21.159Z"/></svg>
                                        </figure>
                                        <div class="title-init">
                                            <p>Duration</p>
                                            <span>{{$r->duration}} {{ $r->duration == 1 ? __('common.day') : 'Days' }}</span>
                                        </div>
                                    </li>
                                    <li>
                                        <figure>
                                            <svg width="800px" height="800px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <g><path d="M4 8H20M4 8V16.8002C4 17.9203 4 18.4801 4.21799 18.9079C4.40973 19.2842 4.71547 19.5905 5.0918 19.7822C5.5192 20 6.07899 20 7.19691 20H16.8031C17.921 20 18.48 20 18.9074 19.7822C19.2837 19.5905 19.5905 19.2842 19.7822 18.9079C20 18.4805 20 17.9215 20 16.8036V8M4 8V7.2002C4 6.08009 4 5.51962 4.21799 5.0918C4.40973 4.71547 4.71547 4.40973 5.0918 4.21799C5.51962 4 6.08009 4 7.2002 4H8M20 8V7.19691C20 6.07899 20 5.5192 19.7822 5.0918C19.5905 4.71547 19.2837 4.40973 18.9074 4.21799C18.4796 4 17.9203 4 16.8002 4H16M16 2V4M16 4H8M8 2V4" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></g>
                                            </svg>
                                        </figure>
                                        <div class="title-init">
                                            <p>Upcoming</p>
                                            <span>{{$r->upComingFormat}}</span>
                                        </div>
                                    </li>
                                    <li class="price-mob d-flex">
                                        <figure>
                                            <svg fill="#000000" width="800px" height="800px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M12,22a1,1,0,0,0,.707-.293l9-9a1,1,0,0,0,.29-.784l-.643-8.357a1,1,0,0,0-.92-.921L12.077,2a1,1,0,0,0-.784.29l-9,9a1,1,0,0,0,0,1.414l9,9A1,1,0,0,0,12,22Zm.382-17.968,7.044.542.542,7.044L12,19.586,4.414,12Zm2.061,5.525a2,2,0,1,1,2.828,0A2,2,0,0,1,14.443,9.557Z"/></svg>
                                        </figure>
                                        <div class="title-init">
                                            <p>@lang("common.price")</p>
                                            <span>£{{$r->price}}</span>
                                        </div>
                                    </li>
                                </ul>
                                <a href="{{route('course.show', $r->course)}}" class="btn-reg"><svg width="800px" height="800px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M15 11C15 12.6569 13.6569 14 12 14C10.3431 14 9 12.6569 9 11M4 7H20M4 7V13C4 19.3668 5.12797 20.5 12 20.5C18.872 20.5 20 19.3668 20 13V7M4 7L5.44721 4.10557C5.786 3.428 6.47852 3 7.23607 3H16.7639C17.5215 3 18.214 3.428 18.5528 4.10557L20 7" stroke="#000000" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                    </svg> View Details and Register </a>
                            </div>
                            <div class="l-item__img">
                                <figure><img src="{{Storage::url($r->course->image)}}" alt="" /></figure>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
                    </div>
                    <!-- /.swiper-wrapper -->
                    <div class="swiper-pagination1"></div>
                    <!-- /.swiper-pagination1 -->
                </div>
                <!-- /.swiper-container -->
            </div>
        </div>
    </section>
    @endif


    <section class="section partners partners-full">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="section-title">
                        <div class="icon">
                            <svg xmlns="http://www.w3.org/2000/svg" width="1284.000000pt" height="1058.000000pt" viewBox="0 0 1284.000000 1058.000000" preserveAspectRatio="xMidYMid meet">
                                <g transform="translate(0.000000,1058.000000) scale(0.100000,-0.100000)"
                                   fill="#000000" stroke="none">
                                    <path d="M5409 8514 c-80 -71 -174 -154 -210 -185 -35 -31 -224 -197 -419
                           -369 -195 -172 -440 -388 -544 -479 -104 -91 -196 -173 -205 -181 -9 -8 -144
                           -127 -301 -265 -157 -137 -328 -287 -380 -334 -52 -46 -108 -96 -125 -110 -16
                           -15 -187 -165 -380 -335 -192 -170 -399 -352 -460 -404 -226 -197 -277 -265
                           -311 -419 -20 -92 -14 -189 17 -281 l21 -63 27 24 c14 13 37 34 51 46 82 74
                           203 140 327 177 78 24 105 27 238 28 223 1 268 -15 570 -198 502 -304 578
                           -348 621 -363 62 -21 164 -13 224 17 25 13 122 90 215 173 94 82 313 275 487
                           428 174 153 345 311 379 351 115 134 199 284 253 458 41 128 72 368 47 358 -4
                           -1 -39 -31 -77 -64 -38 -34 -172 -153 -299 -264 -126 -111 -355 -313 -508
                           -449 -154 -135 -300 -259 -327 -274 -103 -60 -225 -43 -312 44 -29 29 -48 56
                           -46 67 3 14 224 215 503 455 33 29 85 75 115 102 30 28 89 80 130 115 41 36
                           77 67 80 70 3 3 41 37 85 76 44 39 127 112 185 163 58 51 131 123 164 160 166
                           187 278 435 306 678 21 178 54 194 -366 -176 -205 -179 -405 -356 -445 -392
                           -41 -36 -150 -132 -244 -214 -93 -82 -193 -169 -220 -194 -28 -25 -100 -89
                           -160 -141 -61 -52 -128 -111 -149 -130 -57 -51 -54 -49 -146 -130 -47 -41 -87
                           -77 -90 -80 -32 -32 -37 -32 -130 15 -50 25 -130 56 -177 69 l-86 22 124 110
                           c68 60 205 181 304 269 100 88 286 252 415 365 129 113 259 228 290 255 97 87
                           203 181 261 231 230 199 457 404 504 457 159 174 276 423 310 656 9 60 15 126
                           13 146 l-3 37 -146 -128z"/>
                                    <path d="M1563 8149 c13 -196 91 -423 204 -591 72 -107 148 -190 303 -326 385
                           -341 519 -458 624 -551 51 -44 61 -49 75 -38 9 8 57 49 106 92 197 172 211
                           187 186 197 -5 2 -60 48 -122 103 -62 55 -158 140 -213 189 -56 49 -117 103
                           -136 120 -19 17 -163 144 -320 281 -157 137 -290 254 -296 260 -73 67 -399
                           351 -407 354 -7 3 -8 -25 -4 -90z"/>
                                    <path d="M1560 7168 c0 -201 92 -467 228 -663 70 -101 117 -151 265 -282 104
                           -93 120 -103 135 -92 9 8 71 61 137 120 66 58 130 114 143 125 12 10 22 21 22
                           25 0 4 -86 82 -192 175 -204 180 -450 397 -508 448 -19 17 -73 65 -120 106
                           -47 41 -86 78 -88 83 -11 23 -22 0 -22 -45z"/>

                                </g>
                            </svg>

                        </div>
                        <!-- /.icon -->
                        <h1>@lang("common.partners_title_2")</h1>
                    </div>
                    <!-- /.section-title -->
                </div>
                <!-- /.col-md-12 -->
                <div class="col-md-12">
                    <div class="partners-slider">
                        <div class="partners-slider__carousel swiper-container">
                            <div class="swiper-wrapper">
                                @foreach($clients as $client)
                                    <div class="swiper-slide">
                                        <div class="partners-item__carousel-item">
                                            <a href="{{$client->link}}" title="{{$client->name}}">
                                                <figure>
                                                    <img src="{{Storage::url($client->image)}}" alt="{{$client->name}}" />
                                                </figure>
                                            </a>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
                <!-- /.col-md-12 -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container -->
    </section>


    <!-- For Modal -->
    <div class="modal largemodal fade" id="form" tabindex="-1" role="dialog" aria-labelledby="form" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <form action="{{route('contact.enquiry')}}" class="valid-form modal-content" method="post">
                <div class="modal-header">
                    <div class="title-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="1284.000000pt" height="1058.000000pt" viewBox="0 0 1284.000000 1058.000000" preserveAspectRatio="xMidYMid meet">
                            <g transform="translate(0.000000,1058.000000) scale(0.100000,-0.100000)"
                               fill="#000000" stroke="none">
                                <path d="M5409 8514 c-80 -71 -174 -154 -210 -185 -35 -31 -224 -197 -419
                           -369 -195 -172 -440 -388 -544 -479 -104 -91 -196 -173 -205 -181 -9 -8 -144
                           -127 -301 -265 -157 -137 -328 -287 -380 -334 -52 -46 -108 -96 -125 -110 -16
                           -15 -187 -165 -380 -335 -192 -170 -399 -352 -460 -404 -226 -197 -277 -265
                           -311 -419 -20 -92 -14 -189 17 -281 l21 -63 27 24 c14 13 37 34 51 46 82 74
                           203 140 327 177 78 24 105 27 238 28 223 1 268 -15 570 -198 502 -304 578
                           -348 621 -363 62 -21 164 -13 224 17 25 13 122 90 215 173 94 82 313 275 487
                           428 174 153 345 311 379 351 115 134 199 284 253 458 41 128 72 368 47 358 -4
                           -1 -39 -31 -77 -64 -38 -34 -172 -153 -299 -264 -126 -111 -355 -313 -508
                           -449 -154 -135 -300 -259 -327 -274 -103 -60 -225 -43 -312 44 -29 29 -48 56
                           -46 67 3 14 224 215 503 455 33 29 85 75 115 102 30 28 89 80 130 115 41 36
                           77 67 80 70 3 3 41 37 85 76 44 39 127 112 185 163 58 51 131 123 164 160 166
                           187 278 435 306 678 21 178 54 194 -366 -176 -205 -179 -405 -356 -445 -392
                           -41 -36 -150 -132 -244 -214 -93 -82 -193 -169 -220 -194 -28 -25 -100 -89
                           -160 -141 -61 -52 -128 -111 -149 -130 -57 -51 -54 -49 -146 -130 -47 -41 -87
                           -77 -90 -80 -32 -32 -37 -32 -130 15 -50 25 -130 56 -177 69 l-86 22 124 110
                           c68 60 205 181 304 269 100 88 286 252 415 365 129 113 259 228 290 255 97 87
                           203 181 261 231 230 199 457 404 504 457 159 174 276 423 310 656 9 60 15 126
                           13 146 l-3 37 -146 -128z"/>
                                <path d="M1563 8149 c13 -196 91 -423 204 -591 72 -107 148 -190 303 -326 385
                           -341 519 -458 624 -551 51 -44 61 -49 75 -38 9 8 57 49 106 92 197 172 211
                           187 186 197 -5 2 -60 48 -122 103 -62 55 -158 140 -213 189 -56 49 -117 103
                           -136 120 -19 17 -163 144 -320 281 -157 137 -290 254 -296 260 -73 67 -399
                           351 -407 354 -7 3 -8 -25 -4 -90z"/>
                                <path d="M1560 7168 c0 -201 92 -467 228 -663 70 -101 117 -151 265 -282 104
                           -93 120 -103 135 -92 9 8 71 61 137 120 66 58 130 114 143 125 12 10 22 21 22
                           25 0 4 -86 82 -192 175 -204 180 -450 397 -508 448 -19 17 -73 65 -120 106
                           -47 41 -86 78 -88 83 -11 23 -22 0 -22 -45z"/>
                                <path d="M1880 3849 l0 -480 278 3 c258 3 281 5 333 26 59 24 111 68 136 116
                           20 39 28 132 14 181 -15 56 -66 122 -113 146 -22 11 -36 24 -32 28 5 4 26 20
                           48 35 105 78 99 264 -11 355 -78 64 -115 71 -398 71 l-255 0 0 -481z m548 349
                           c57 -38 75 -69 76 -131 0 -67 -27 -109 -87 -132 -34 -13 -77 -15 -232 -13
                           l-190 3 -3 140 c-1 76 0 145 3 153 4 11 40 13 198 10 192 -3 194 -3 235 -30z
                           m-15 -408 c67 -25 102 -62 117 -122 11 -43 10 -52 -11 -95 -21 -41 -32 -51
                           -79 -70 -51 -21 -68 -22 -250 -20 l-195 2 -3 150 c-1 82 0 155 2 162 4 10 47
                           13 185 13 156 0 188 -3 234 -20z"/>
                                <path d="M4360 4310 c-24 -24 -26 -67 -4 -98 20 -28 83 -31 107 -4 25 28 22
                           87 -5 106 -31 22 -74 20 -98 -4z"/>
                                <path d="M7950 3850 l0 -480 360 0 360 0 0 85 0 85 -272 2 -273 3 -3 90 c-2
                           50 0 100 3 113 l5 22 260 0 260 0 0 80 0 80 -262 2 -263 3 0 110 0 110 270 5
                           270 5 3 83 3 82 -361 0 -360 0 0 -480z"/>
                                <path d="M10410 3850 l0 -480 80 0 80 0 0 480 0 480 -80 0 -80 0 0 -480z"/>
                                <path d="M10145 4074 l-30 -36 -54 16 c-97 29 -205 15 -295 -38 -54 -32 -86
                           -69 -122 -142 -24 -50 -28 -69 -28 -148 0 -115 24 -181 90 -248 56 -57 130
                           -87 229 -95 92 -6 126 -18 159 -52 75 -78 27 -206 -88 -232 -99 -22 -207 32
                           -226 113 -6 22 -11 23 -85 23 l-80 0 3 -34 c7 -94 91 -198 188 -234 27 -10 83
                           -21 124 -24 238 -17 403 150 360 365 -10 50 -19 64 -67 110 l-56 53 40 34 c64
                           55 87 113 87 220 1 64 -4 99 -15 120 -9 17 -23 43 -32 59 -15 28 -15 30 20 73
                           20 25 32 47 27 52 -42 37 -103 81 -110 81 -5 0 -22 -17 -39 -36z m-123 -169
                           c46 -19 95 -76 104 -120 11 -62 0 -141 -27 -179 -27 -41 -93 -76 -141 -76 -49
                           1 -112 33 -144 76 -26 34 -29 45 -29 119 0 74 3 85 28 118 53 69 135 94 209
                           62z"/>
                                <path d="M10957 4054 c-31 -7 -71 -21 -87 -29 -198 -102 -234 -434 -63 -585
                           72 -63 139 -85 263 -84 83 0 116 5 159 22 60 24 121 62 121 77 0 5 -21 30 -46
                           56 l-46 47 -51 -27 c-43 -22 -64 -26 -132 -26 -115 0 -182 39 -201 118 l-6 27
                           267 0 268 0 -5 93 c-10 183 -95 285 -263 316 -74 14 -105 13 -178 -5z m208
                           -157 c40 -22 83 -87 70 -107 -4 -6 -73 -10 -181 -10 -194 0 -195 1 -155 66 43
                           71 183 98 266 51z"/>
                                <path d="M3777 4044 c-70 -22 -133 -88 -142 -148 -8 -52 12 -121 43 -149 33
                           -30 108 -56 218 -75 162 -29 203 -56 190 -126 -12 -63 -72 -96 -177 -96 -77 0
                           -147 23 -195 64 -43 37 -48 36 -82 -8 l-23 -29 28 -30 c115 -120 401 -127 513
                           -12 29 30 34 43 38 95 6 76 -1 102 -38 144 -38 42 -86 62 -199 80 -131 21
                           -168 34 -190 63 -37 46 -27 92 27 125 29 18 51 22 116 22 70 1 89 -3 140 -28
                           l58 -29 31 31 c37 38 31 49 -52 89 -47 23 -71 27 -156 30 -69 2 -115 -2 -148
                           -13z"/>
                                <path d="M4927 4046 c-49 -18 -116 -63 -124 -81 -11 -29 -22 -14 -25 33 l-3
                           47 -47 3 -48 3 0 -340 0 -341 50 0 50 0 0 191 c0 232 7 266 69 330 88 90 210
                           92 297 5 55 -55 64 -101 64 -336 l0 -190 50 0 c45 0 50 2 56 26 4 15 4 118 1
                           228 -5 171 -9 210 -27 258 -23 63 -85 127 -147 153 -54 23 -169 29 -216 11z"/>
                                <path d="M5720 4051 c-92 -30 -179 -104 -211 -179 -79 -184 -14 -400 144 -477
                           55 -26 164 -46 213 -38 114 18 171 40 229 87 l30 25 -34 35 -34 35 -41 -28
                           c-149 -102 -368 -52 -416 93 -6 20 -10 41 -8 46 2 7 104 12 281 15 153 3 283
                           8 289 11 17 10 1 148 -22 203 -27 61 -90 123 -155 152 -38 18 -72 24 -145 26
                           -52 1 -106 -1 -120 -6z m218 -106 c20 -9 48 -26 61 -38 28 -26 60 -92 61 -124
                           l0 -23 -235 0 c-230 0 -235 0 -235 20 0 28 33 90 62 117 47 44 98 62 176 63
                           44 0 87 -6 110 -15z"/>
                                <path d="M6438 4043 c-52 -17 -114 -70 -129 -110 -15 -40 -10 -116 10 -153 25
                           -49 103 -85 221 -105 169 -28 210 -51 210 -114 0 -77 -76 -117 -205 -109 -75
                           5 -140 31 -186 75 l-26 24 -27 -31 c-14 -17 -26 -38 -26 -45 0 -7 17 -27 38
                           -44 113 -94 358 -106 461 -23 62 50 76 77 76 145 0 122 -62 172 -250 202 -154
                           25 -205 54 -205 118 0 32 48 73 103 88 58 15 148 3 213 -29 l53 -26 26 30 c14
                           16 25 32 25 36 0 14 -78 59 -126 74 -62 18 -196 17 -256 -3z"/>
                                <path d="M7141 4048 c-54 -15 -117 -63 -136 -104 -27 -57 -17 -145 21 -185 41
                           -43 97 -66 206 -84 168 -29 208 -51 208 -116 0 -77 -75 -116 -208 -106 -78 6
                           -137 30 -183 74 l-27 25 -26 -35 c-14 -18 -26 -39 -26 -45 0 -6 17 -24 38 -41
                           113 -94 358 -106 461 -23 54 44 72 74 77 133 12 122 -66 188 -253 215 -162 24
                           -230 74 -193 142 27 46 76 67 161 67 64 0 87 -5 138 -29 l60 -29 23 29 c13 15
                           23 33 23 38 0 17 -65 55 -118 71 -56 17 -193 19 -246 3z"/>
                                <path d="M8983 4041 c-141 -48 -218 -169 -216 -341 2 -210 119 -337 319 -348
                           72 -4 89 -1 135 20 30 14 65 37 79 51 33 35 38 34 42 -10 l3 -38 78 -3 77 -3
                           -2 338 -3 338 -75 0 -75 0 -3 -37 c-4 -43 -11 -47 -38 -17 -10 12 -41 32 -69
                           45 -63 30 -174 32 -252 5z m218 -136 c84 -30 128 -98 129 -198 0 -80 -30 -141
                           -89 -179 -42 -27 -52 -29 -118 -26 -62 3 -76 7 -110 34 -61 50 -78 87 -78 174
                           0 65 4 81 28 117 50 78 147 110 238 78z"/>
                                <path d="M11677 4046 c-21 -8 -56 -26 -77 -40 -105 -69 -106 -228 -3 -299 52
                           -35 77 -44 174 -57 113 -16 148 -28 161 -55 13 -30 -13 -82 -50 -96 -46 -18
                           -151 -6 -212 24 -30 14 -63 32 -73 39 -16 11 -22 9 -43 -17 -14 -16 -31 -42
                           -39 -56 -13 -25 -13 -29 12 -53 87 -87 286 -117 429 -65 193 72 190 333 -5
                           389 -20 6 -74 15 -119 21 -46 5 -97 16 -113 24 -33 17 -46 54 -30 83 26 48
                           158 55 245 13 l48 -24 39 44 c21 24 37 51 36 59 -1 8 -29 28 -61 45 -54 27
                           -69 30 -170 32 -76 2 -123 -1 -149 -11z"/>
                                <path d="M2814 4036 c-3 -8 -4 -113 -2 -233 5 -243 12 -280 72 -349 48 -56
                           116 -87 199 -92 88 -5 147 12 208 60 27 21 51 38 53 38 3 0 6 -19 8 -42 l3
                           -43 48 -3 47 -3 0 341 0 341 -52 -3 -53 -3 -5 -220 c-3 -121 -9 -229 -14 -240
                           -65 -141 -262 -171 -359 -55 -41 48 -47 90 -47 313 l0 207 -50 0 c-35 0 -52
                           -5 -56 -14z"/>
                                <path d="M4360 3710 l0 -340 50 0 50 0 0 340 0 340 -50 0 -50 0 0 -340z"/>
                                <path d="M4585 3098 c-25 -7 -58 -30 -91 -63 -44 -44 -54 -60 -60 -101 -31
                           -206 204 -334 364 -200 17 15 32 32 32 37 0 15 -47 10 -61 -7 -22 -27 -71 -46
                           -119 -46 -158 0 -237 192 -123 299 62 59 151 66 225 17 21 -13 47 -24 60 -24
                           l21 0 -24 26 c-53 57 -147 83 -224 62z"/>
                                <path d="M7125 3101 c-188 -48 -233 -273 -77 -385 40 -28 51 -31 122 -31 71 0
                           82 3 122 30 42 30 62 65 36 65 -7 0 -33 -14 -57 -30 -75 -51 -168 -42 -228 22
                           -99 105 -39 272 105 294 46 7 120 -15 142 -42 7 -7 24 -14 38 -14 l25 0 -24
                           26 c-49 53 -139 82 -204 65z"/>
                                <path d="M1960 2890 l0 -213 94 5 c108 6 159 26 193 76 28 43 39 128 24 199
                           -22 111 -58 135 -208 141 l-103 4 0 -212z m221 149 c36 -19 54 -69 54 -151 0
                           -62 -4 -82 -21 -105 -33 -45 -80 -64 -154 -61 l-65 3 -3 167 -2 167 47 3 c52
                           3 114 -7 144 -23z"/>
                                <path d="M7740 2889 c0 -164 3 -210 13 -207 9 4 13 57 15 211 2 180 0 207 -13
                           207 -13 0 -15 -29 -15 -211z"/>
                                <path d="M8170 2890 c0 -173 2 -210 14 -210 8 0 17 7 20 15 5 14 9 14 35 0
                           111 -57 231 50 185 164 -28 71 -119 103 -184 66 -14 -8 -28 -14 -32 -15 -5 0
                           -8 43 -8 95 0 78 -3 95 -15 95 -13 0 -15 -30 -15 -210z m179 6 c68 -36 66
                           -134 -4 -172 -80 -43 -168 46 -127 128 27 53 78 71 131 44z"/>
                                <path d="M2353 3039 c-16 -16 -5 -39 19 -39 14 0 19 6 16 22 -4 25 -19 33 -35
                           17z"/>
                                <path d="M6470 2995 c0 -34 -5 -47 -20 -55 -11 -6 -20 -15 -20 -19 0 -5 9 -11
                           20 -14 18 -5 20 -14 20 -116 0 -91 3 -111 15 -111 12 0 15 22 17 113 l3 112
                           28 3 c15 2 27 9 27 17 0 8 -12 15 -27 17 -26 3 -28 7 -31 51 -2 33 -7 47 -18
                           47 -10 0 -14 -12 -14 -45z"/>
                                <path d="M9040 2994 c0 -37 -4 -47 -20 -51 -11 -3 -20 -11 -20 -18 0 -7 9 -15
                           20 -18 18 -5 20 -14 20 -116 0 -92 3 -111 15 -111 12 0 15 19 15 115 l0 115
                           31 0 c38 0 35 24 -4 28 -25 3 -27 7 -27 53 0 37 -4 49 -15 49 -11 0 -15 -12
                           -15 -46z"/>
                                <path d="M2355 2928 c-3 -8 -4 -66 -3 -129 2 -93 6 -114 18 -114 13 0 15 20
                           15 124 0 91 -3 125 -12 128 -7 3 -15 -2 -18 -9z"/>
                                <path d="M2490 2920 c-40 -40 -14 -102 47 -116 65 -14 78 -21 81 -44 7 -48
                           -64 -67 -111 -30 -30 24 -51 26 -43 4 3 -9 6 -17 6 -20 0 -10 63 -34 90 -34
                           33 0 70 15 82 34 14 20 8 73 -9 89 -10 8 -39 21 -65 27 -26 7 -53 18 -59 26
                           -31 37 35 69 84 41 35 -21 47 -21 47 -3 0 44 -113 63 -150 26z"/>
                                <path d="M2767 2921 c-59 -38 -76 -100 -48 -168 36 -88 186 -96 231 -14 15 30
                           -9 28 -56 -4 -43 -29 -66 -31 -104 -8 -67 39 -64 140 5 171 38 18 58 15 99
                           -13 52 -35 67 -34 46 6 -26 47 -122 64 -173 30z"/>
                                <path d="M3055 2908 c-34 -31 -39 -41 -43 -91 -4 -55 -3 -58 36 -97 36 -36 44
                           -40 91 -40 44 0 56 5 89 34 35 33 37 38 37 95 0 55 -3 64 -32 92 -26 26 -41
                           33 -86 37 -51 4 -57 2 -92 -30z m134 -14 c13 -9 29 -32 37 -50 12 -29 12 -39
                           0 -68 -29 -68 -94 -86 -146 -38 -26 22 -31 35 -32 71 0 38 4 49 31 73 36 32
                           75 36 110 12z"/>
                                <path d="M3310 2928 c1 -30 99 -243 113 -246 9 -2 22 7 31 20 21 33 98 229 92
                           235 -15 15 -36 -14 -71 -96 -21 -51 -43 -90 -48 -88 -6 2 -27 43 -47 93 -23
                           57 -42 90 -53 92 -9 2 -17 -2 -17 -10z"/>
                                <path d="M3648 2924 c-82 -44 -78 -184 7 -230 45 -24 119 -15 153 20 23 22 24
                           26 8 32 -10 4 -24 0 -35 -10 -10 -9 -34 -19 -53 -22 -27 -5 -40 -1 -67 21 -55
                           47 -41 58 79 59 100 1 105 2 108 23 5 32 -24 81 -61 103 -38 24 -101 25 -139
                           4z m140 -49 c12 -14 22 -30 22 -36 0 -5 -40 -9 -90 -9 -89 0 -103 6 -82 36 34
                           50 110 54 150 9z"/>
                                <path d="M3919 2933 c-5 -26 -6 -221 -2 -235 2 -10 11 -18 19 -18 11 0 14 18
                           14 84 0 92 17 128 65 140 26 7 35 36 10 36 -8 0 -26 -6 -41 -12 -22 -10 -28
                           -10 -32 0 -4 13 -31 17 -33 5z"/>
                                <path d="M4943 2919 c-20 -12 -38 -37 -48 -64 -22 -55 -9 -101 41 -145 28 -24
                           43 -30 83 -30 128 0 178 158 75 239 -36 28 -108 28 -151 0z m138 -38 c23 -23
                           29 -38 29 -71 0 -33 -6 -48 -29 -71 -39 -38 -80 -39 -123 0 -27 24 -33 36 -33
                           71 0 35 6 47 33 71 43 39 84 38 123 0z"/>
                                <path d="M5220 2810 c0 -109 2 -130 15 -130 8 0 15 8 16 18 5 164 5 165 33
                           189 47 40 108 25 121 -30 3 -12 6 -55 7 -96 2 -49 7 -76 16 -79 9 -3 12 23 12
                           105 0 102 -2 111 -23 131 -26 24 -93 30 -129 11 -14 -8 -23 -8 -31 0 -6 6 -17
                           11 -24 11 -10 0 -13 -30 -13 -130z"/>
                                <path d="M5530 2810 c0 -109 2 -130 15 -130 12 0 15 15 15 74 0 93 13 131 50
                           146 35 14 85 5 96 -18 4 -9 10 -57 14 -106 3 -55 10 -91 17 -93 16 -6 19 26
                           13 130 -4 78 -8 91 -29 108 -27 22 -110 26 -129 7 -9 -9 -15 -9 -24 0 -32 32
                           -38 14 -38 -118z"/>
                                <path d="M5873 2918 c-72 -52 -76 -147 -8 -207 48 -42 118 -44 164 -5 17 14
                           31 30 31 35 0 16 -31 10 -57 -11 -35 -27 -75 -25 -113 6 -53 45 -39 56 80 57
                           l105 2 3 32 c3 25 -4 40 -31 69 -29 32 -41 38 -88 41 -45 4 -60 0 -86 -19z
                           m145 -43 c12 -14 22 -30 22 -36 0 -5 -40 -9 -90 -9 -102 0 -112 10 -58 55 26
                           22 39 26 67 21 21 -3 45 -16 59 -31z"/>
                                <path d="M6172 2906 c-35 -33 -37 -38 -37 -96 0 -58 2 -63 37 -96 32 -29 45
                           -34 88 -34 41 0 55 5 85 31 19 17 35 35 35 40 0 17 -32 9 -62 -16 -54 -45
                           -124 -22 -148 49 -19 58 17 112 82 122 27 5 40 1 65 -20 41 -34 80 -35 55 -1
                           -24 34 -67 55 -113 55 -42 0 -55 -5 -87 -34z"/>
                                <path d="M7468 2922 c-36 -21 -58 -69 -58 -122 0 -36 6 -48 39 -81 35 -35 44
                           -39 90 -39 42 0 56 5 83 29 42 38 28 52 -22 23 -51 -29 -81 -28 -119 4 -56 47
                           -42 59 61 54 26 -1 66 1 88 5 47 8 52 30 20 82 -36 60 -124 82 -182 45z m140
                           -47 c12 -14 22 -30 22 -36 0 -5 -40 -9 -90 -9 -102 0 -112 10 -58 55 26 22 39
                           26 67 21 21 -3 45 -16 59 -31z"/>
                                <path d="M7893 2918 c-39 -28 -53 -57 -53 -109 0 -78 53 -129 134 -129 36 0
                           52 6 78 29 42 38 28 52 -22 23 -21 -12 -50 -22 -64 -22 -44 0 -115 78 -73 81
                           6 0 53 2 102 4 50 1 93 3 98 4 14 2 7 46 -13 78 -38 63 -127 83 -187 41z m145
                           -43 c12 -14 22 -30 22 -36 0 -5 -40 -9 -90 -9 -102 0 -112 10 -58 55 26 22 39
                           26 67 21 21 -3 45 -16 59 -31z"/>
                                <path d="M8504 2927 c-2 -7 -3 -65 -2 -128 2 -93 6 -114 18 -114 12 0 16 17
                           18 80 3 95 14 121 59 137 18 7 33 18 33 25 0 16 -42 17 -58 1 -10 -10 -17 -10
                           -32 0 -25 15 -29 15 -36 -1z"/>
                                <path d="M8723 2918 c-103 -73 -53 -238 72 -238 20 0 49 7 66 15 26 14 30 14
                           35 0 3 -8 12 -15 20 -15 11 0 14 26 14 130 0 104 -3 130 -14 130 -8 0 -17 -7
                           -20 -15 -5 -14 -9 -14 -35 0 -43 22 -101 19 -138 -7z m117 -23 c36 -19 50 -43
                           50 -88 0 -73 -77 -118 -138 -81 -67 39 -69 127 -4 167 34 21 55 21 92 2z"/>
                                <path d="M9213 2907 c-32 -30 -37 -41 -41 -90 -4 -55 -3 -58 36 -97 35 -35 45
                           -40 87 -40 48 0 100 23 110 48 10 26 -5 27 -45 4 -53 -30 -85 -28 -121 7 -45
                           46 -35 59 41 52 14 -2 53 0 88 4 61 6 62 7 62 37 0 22 -11 41 -39 69 -35 35
                           -44 39 -90 39 -44 0 -56 -5 -88 -33z m146 -23 c53 -44 43 -54 -59 -54 -71 0
                           -90 3 -90 14 0 23 59 66 90 66 16 0 42 -11 59 -26z"/>
                                <path d="M4188 2815 c-16 -35 -1 -59 40 -63 27 -3 32 1 38 24 9 35 -6 58 -39
                           62 -21 3 -30 -3 -39 -23z"/>
                                <path d="M6725 2824 c-34 -35 -1 -83 48 -70 21 5 27 12 27 34 0 49 -42 69 -75
                           36z"/>
                            </g>
                        </svg>
                        <h2 class="modal-title" id="exampleModalLabel">@lang('common.enq_title_2')</h2>

                    </div>
                    <!-- /.title-2 -->
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <i class="icofont-close"></i>
                    </button>
                </div>
                <div class="modal-body">

                    <div class="form">
                        <div class="row">
                            @csrf
                            <input type="hidden" name="type" value="course">
                            <input type="hidden" name="course_id" value="{{ $course->id }}">
                            <input type="hidden" name="course_title" value="{{ $course->title }}">
                            <input name="model_type" value="{{\App\Models\Course::class}}" hidden />
                            <input name="model_id" value="{{$course->id}}" hidden />
                            <div class="col-md-6">
                                <div class="loginform-form">
                                    <input name="first_name" class="f-input" type="text" placeholder="@lang('common.first_name')" value="" data-validation="required">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="loginform-form">
                                    <input name="last_name" class="f-input" type="text" placeholder="@lang('common.last_name')" data-validation="required">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="loginform-form">
                                    <input name="email" class="f-input" type="email" placeholder="@lang('common.email')" data-validation="required">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="loginform-form">
                                    <input name="mobile" type="tel" placeholder="@lang('common.mobile_number')" data-validation="required">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="loginform-form">
                                    <input name="job" class="f-input" type="text" placeholder="@lang('common.jobs_name')" data-validation="required">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="loginform-form">
                                    <input name="company" class="f-input" type="text" placeholder="@lang('common.company_name')" data-validation="required">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="loginform-form">
                                    <select name="more_info" class="select">
                                        <option value="">I Would Like More Info On</option>
                                        <option value="Course Outlines">Course Outlines</option>
                                        <option value="Venue and Date Options">Venue and Date Options</option>
                                        <option value="Corporate Discounts">Corporate Discounts</option>
                                        <option value="Registration Options">Registration Options</option>
                                        <option value="Other">Other</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="loginform-form">
                                    <select name="know_by" class="select">
                                        <option value="">How Did You Hear About Us?</option>
                                        <option value="Google">Google</option>
                                        <option value="Email">Email</option>
                                        <option value="Referral">Referral</option>
                                        <option value="Facebook / Linkedin / Twitter">Facebook / Linkedin / Twitter</option>
                                        <option value="Findcourses">Findcourses</option>
                                        <option value="Laimoon">Laimoon</option>
                                        <option value="Edarabia">Edarabia</option>
                                        <option value="Emagister">Emagister</option>
                                        <option value="Bayt">Bayt</option>
                                        <option value="Reed">Reed</option>
                                        <option value="Existing Customer">Existing Customer</option>
                                        <option value="Other">Other</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-12">
                                <div class="loginform-form">
                                    <textarea name="message" class="textarea" rows="4" placeholder="@lang('common.text_sms')" required></textarea>
                                </div>
                            </div>

                            <!-- Review Us Section -->
                            <div class="col-md-12">
                                <div class="review-section" id="review-section-course">
                                    <h4><i class="icofont-star"></i> Review Us</h4>
                                    <p class="review-description">Share your experience with our course program (optional)</p>

                                    <div class="loginform-form">
                                        <textarea name="review_text" class="textarea" rows="3" placeholder="Tell us about your experience with this course..."></textarea>
                                    </div>

                                    <div class="rating-section">
                                        <label class="rating-label">Rate your experience:</label>
                                        <div class="star-rating">
                                            <input type="radio" name="rating" value="5" id="course-star5">
                                            <label for="course-star5" title="5 stars">★</label>
                                            <input type="radio" name="rating" value="4" id="course-star4">
                                            <label for="course-star4" title="4 stars">★</label>
                                            <input type="radio" name="rating" value="3" id="course-star3">
                                            <label for="course-star3" title="3 stars">★</label>
                                            <input type="radio" name="rating" value="2" id="course-star2">
                                            <label for="course-star2" title="2 stars">★</label>
                                            <input type="radio" name="rating" value="1" id="course-star1">
                                            <label for="course-star1" title="1 star">★</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-12">
                                <div class="ft">By submitting this form, you agree to our <a target="_blank" href="https://business-eagles.com/terms-and-conditions">Terms and Conditions</a> and <a target="_blank" href="https://business-eagles.com/privacy-policy">Privacy Policy.</a></div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-primary">Submit</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <style>
        /* Review Us Section Styling for Course */
        .review-section {
            margin-top: 20px !important;
            padding: 20px !important;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
            border-radius: 10px !important;
            border: 2px solid #007bff !important;
            box-shadow: 0 4px 6px rgba(0, 123, 255, 0.1) !important;
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            height: auto !important;
            overflow: visible !important;
        }

        .review-section h4 {
            margin-bottom: 8px !important;
            color: #007bff !important;
            font-size: 18px !important;
            font-weight: bold !important;
            display: flex !important;
            align-items: center !important;
            gap: 8px !important;
        }

        .review-section h4 i {
            color: #ffc107 !important;
        }

        .review-description {
            margin-bottom: 15px !important;
            color: #6c757d !important;
            font-size: 14px !important;
            font-style: italic !important;
        }

        .rating-section {
            margin-top: 15px !important;
        }

        .rating-label {
            display: block !important;
            margin-bottom: 8px !important;
            color: #495057 !important;
            font-weight: 500 !important;
            font-size: 14px !important;
        }

        .star-rating {
            display: flex !important;
            flex-direction: row-reverse !important;
            justify-content: flex-end !important;
            gap: 5px !important;
        }

        .star-rating input[type="radio"] {
            display: none !important;
        }

        .star-rating label {
            cursor: pointer !important;
            font-size: 24px !important;
            color: #ddd !important;
            transition: color 0.2s ease !important;
            user-select: none !important;
        }

        .star-rating label:hover,
        .star-rating label:hover ~ label,
        .star-rating input[type="radio"]:checked ~ label {
            color: #ffc107 !important;
        }

        .star-rating label:hover {
            transform: scale(1.1) !important;
        }

        .review-section {
            border-left: 4px solid #007bff !important;
            position: relative !important;
        }

        .review-section::before {
            content: "Optional" !important;
            position: absolute !important;
            top: -8px !important;
            right: 15px !important;
            background: #007bff !important;
            color: white !important;
            padding: 2px 8px !important;
            border-radius: 10px !important;
            font-size: 11px !important;
            font-weight: bold !important;
        }
    </style>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Ensure review section is visible
            const reviewSection = document.getElementById('review-section-course');
            if (reviewSection) {
                reviewSection.style.display = 'block';
                reviewSection.style.visibility = 'visible';
                reviewSection.style.height = 'auto';
                console.log('Course review section initialized successfully');
            }

            // Star Rating Functionality for Course
            const starRating = document.querySelectorAll('#review-section-course .star-rating input[type="radio"]');
            const starLabels = document.querySelectorAll('#review-section-course .star-rating label');

            starLabels.forEach((label, index) => {
                label.addEventListener('mouseover', function() {
                    highlightStars(5 - index);
                });

                label.addEventListener('mouseout', function() {
                    const checkedStar = document.querySelector('#review-section-course .star-rating input[type="radio"]:checked');
                    if (checkedStar) {
                        highlightStars(parseInt(checkedStar.value));
                    } else {
                        highlightStars(0);
                    }
                });

                label.addEventListener('click', function() {
                    const rating = this.getAttribute('for').replace('course-star', '');
                    console.log('Selected course rating:', rating);
                });
            });

            function highlightStars(rating) {
                starLabels.forEach((label, index) => {
                    if (5 - index <= rating) {
                        label.style.color = '#ffc107';
                    } else {
                        label.style.color = '#ddd';
                    }
                });
            }
        });
    </script>
    <script>
        // Toast helper (unified look)
        function showToast(message) {
            let container = document.getElementById('be-toast-container');
            if (!container) {
                container = document.createElement('div');
                container.id = 'be-toast-container';
                container.style.position = 'fixed';
                container.style.top = '20px';
                container.style.right = '20px';
                container.style.zIndex = '9999';
                container.style.display = 'flex';
                container.style.flexDirection = 'column';
                container.style.gap = '10px';
                document.body.appendChild(container);
            }

            const toast = document.createElement('div');
            toast.textContent = message;
            toast.style.background = '#d02333';
            toast.style.color = '#fff';
            toast.style.padding = '16px 20px';
            toast.style.borderRadius = '12px';
            toast.style.boxShadow = '0 10px 28px rgba(208,35,51,0.35)';
            toast.style.fontSize = '16px';
            toast.style.fontWeight = '600';
            toast.style.letterSpacing = '0.2px';
            toast.style.opacity = '0';
            toast.style.transform = 'translateY(-6px)';
            toast.style.transition = 'all .25s ease';
            container.appendChild(toast);

            requestAnimationFrame(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translateY(0)';
            });

            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateY(-6px)';
                setTimeout(() => {
                    toast.remove();
                    if (!container.children.length) container.remove();
                }, 250);
            }, 3500);
        }

        // Show toast on course outline download click
        document.addEventListener('DOMContentLoaded', function() {
            const links = document.querySelectorAll('a.course-outline-download');
            links.forEach(link => {
                link.addEventListener('click', function() {
                    // slight delay to ensure browser begins opening the file
                    setTimeout(() => showToast('Thanks! Your download has started.'), 300);
                });
            });
        });
    </script>

@stop

